import time
import pandas as pd
from urllib.parse import quote
import random
from queue import Queue, Empty
import threading
from typing import List, Dict
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
MAX_WORKERS = 3  # Reduced for Selenium (more resource intensive)
BATCH_SIZE = 5  # Reduced batch size for Selenium
RETRY_ATTEMPTS = 3  # Number of retry attempts for failed requests
DELAY_MIN = 2  # Increased minimum delay for Selenium
DELAY_MAX = 5  # Increased maximum delay for Selenium
TIMEOUT = 10  # Selenium timeout in seconds

# Proxy configuration
PROXY_HOST = "core-residential.evomi.com"
PROXY_PORT = "1000"
PROXY_USERNAME = "it41"
PROXY_PASSWORD = "Zglx26lEvuyNSlO0Xrvh"

def create_chrome_driver() -> webdriver.Chrome:
    """Create and configure Chrome WebDriver with proxy."""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Configure proxy
    proxy_string = f"{PROXY_HOST}:{PROXY_PORT}"
    chrome_options.add_argument(f"--proxy-server=http://{proxy_string}")

    # Install ChromeDriver automatically
    service = Service(ChromeDriverManager().install())

    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.set_page_load_timeout(TIMEOUT)

    return driver

def read_urls(filename: str) -> List[str]:
    """Read URLs from file and return as list."""
    with open(filename, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def create_google_search_url(url: str) -> str:
    """Create Google search URL with site: operator."""
    search_query = f"site:{url}"
    encoded_query = quote(search_query)
    return f"https://www.google.com/search?q={encoded_query}&sourceid=chrome&ie=UTF-8"

def check_for_captcha(driver: webdriver.Chrome) -> bool:
    """Check if there's a CAPTCHA on the page."""
    captcha_indicators = [
        "//div[contains(@class, 'g-recaptcha')]",
        "//iframe[contains(@src, 'recaptcha')]",
        "//*[contains(text(), 'unusual traffic')]",
        "//*[contains(text(), 'CAPTCHA')]",
        "//div[@id='captcha-form']"
    ]

    for indicator in captcha_indicators:
        try:
            driver.find_element(By.XPATH, indicator)
            return True
        except NoSuchElementException:
            continue

    return False

def process_url(url: str, driver: webdriver.Chrome) -> Dict:
    """Process a single URL and return result."""
    for attempt in range(RETRY_ATTEMPTS):
        try:
            search_url = create_google_search_url(url)
            logger.info(f"Checking: {search_url} (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")

            # Navigate to Google search
            driver.get(search_url)
            time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))

            # Check for CAPTCHA
            if check_for_captcha(driver):
                logger.warning(f"CAPTCHA detected for {url} (Attempt {attempt + 1})")
                if attempt == RETRY_ATTEMPTS - 1:
                    return {"url": url, "indexation_status": "BotDetection"}
                time.sleep(random.uniform(5, 10))  # Wait longer before retry
                continue

            # Accept cookies if present
            try:
                wait = WebDriverWait(driver, 3)
                cookie_buttons = [
                    "//button[contains(text(), 'Accept all')]",
                    "//button[contains(text(), 'I agree')]",
                    "//button[@id='L2AGLb']",  # Google's "I agree" button
                    "//div[contains(text(), 'Accept all')]"
                ]

                for button_xpath in cookie_buttons:
                    try:
                        cookie_button = wait.until(EC.element_to_be_clickable((By.XPATH, button_xpath)))
                        cookie_button.click()
                        time.sleep(1)
                        break
                    except TimeoutException:
                        continue
            except Exception:
                pass

            # Check for search results
            try:
                # Wait for search results to load
                wait = WebDriverWait(driver, TIMEOUT)
                search_div = wait.until(EC.presence_of_element_located((By.ID, "search")))

                # Look for actual search results
                result_selectors = [
                    "div.MjjYud",  # Main result container
                    "div.g",       # Alternative result container
                    "div[data-ved]"  # Results with data-ved attribute
                ]

                for selector in result_selectors:
                    try:
                        results = driver.find_elements(By.CSS_SELECTOR, selector)
                        if results:
                            logger.info(f"Found {url} Indexed")
                            return {"url": url, "indexation_status": "Yes"}
                    except NoSuchElementException:
                        continue

                # Check if "No results found" or similar message
                no_results_indicators = [
                    "//*[contains(text(), 'did not match any documents')]",
                    "//*[contains(text(), 'No results found')]",
                    "//*[contains(text(), 'Your search')]"
                ]

                for indicator in no_results_indicators:
                    try:
                        driver.find_element(By.XPATH, indicator)
                        logger.info(f"Not found {url} Indexed")
                        return {"url": url, "indexation_status": "No"}
                    except NoSuchElementException:
                        continue

                logger.info(f"Not found {url} Indexed")
                return {"url": url, "indexation_status": "No"}

            except TimeoutException:
                logger.warning(f"Timeout waiting for search results for {url}")
                if attempt == RETRY_ATTEMPTS - 1:
                    return {"url": url, "indexation_status": "Error"}
                continue

        except WebDriverException as e:
            logger.error(f"WebDriver error for {url} (Attempt {attempt + 1}): {str(e)}")
            if attempt == RETRY_ATTEMPTS - 1:
                return {"url": url, "indexation_status": "Error"}
            time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
        except Exception as e:
            logger.error(f"Error for {url} (Attempt {attempt + 1}): {str(e)}")
            if attempt == RETRY_ATTEMPTS - 1:
                return {"url": url, "indexation_status": "Error"}
            time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))

    return {"url": url, "indexation_status": "Error"}

def worker(url_queue: Queue, results_queue: Queue):
    """Worker function to process URLs from queue."""
    driver = None
    try:
        driver = create_chrome_driver()

        while True:
            try:
                url = url_queue.get_nowait()
                try:
                    result = process_url(url, driver)
                    results_queue.put(result)
                finally:
                    url_queue.task_done()  # Only call task_done() if we successfully got an item
            except Empty:
                break
            except Exception as e:
                logger.error(f"Worker error: {str(e)}")
                # Don't call task_done() here as we didn't get an item from the queue
    finally:
        if driver:
            try:
                driver.quit()
            except Exception as e:
                logger.error(f"Error closing driver: {str(e)}")

def process_batch(urls: List[str]) -> List[Dict]:
    """Process a batch of URLs concurrently."""
    url_queue = Queue()
    results_queue = Queue()
    
    # Add URLs to queue
    for url in urls:
        url_queue.put(url)
    
    # Start workers
    threads = []
    for _ in range(min(MAX_WORKERS, len(urls))):
        t = threading.Thread(target=worker, args=(url_queue, results_queue))
        t.start()
        threads.append(t)
    
    # Wait for all URLs to be processed
    url_queue.join()
    
    # Collect results
    results = []
    while not results_queue.empty():
        results.append(results_queue.get())
    
    return results

class GISChecker:
    """Django-compatible GIS indexation checker using Selenium."""

    def __init__(self, max_workers: int = MAX_WORKERS, batch_size: int = BATCH_SIZE):
        self.max_workers = max_workers
        self.batch_size = batch_size

    def check_single_url(self, url: str) -> Dict:
        """Check a single URL for indexation status. Django-compatible method."""
        driver = None
        try:
            driver = create_chrome_driver()
            result = process_url(url, driver)
            return result
        except Exception as e:
            logger.error(f"Error checking single URL {url}: {str(e)}")
            return {"url": url, "indexation_status": "Error"}
        finally:
            if driver:
                try:
                    driver.quit()
                except Exception as e:
                    logger.error(f"Error closing driver: {str(e)}")

    def check_urls_batch(self, urls: List[str]) -> List[Dict]:
        """Check multiple URLs in batch. Django-compatible method."""
        return process_batch(urls)

    def check_urls_from_file(self, filename: str) -> List[Dict]:
        """Check URLs from file. Original functionality."""
        urls = read_urls(filename)
        logger.info(f"Loaded {len(urls)} URLs")

        # Process URLs in batches
        all_results = []
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            logger.info(f"Processing batch {i//self.batch_size + 1}/{(len(urls) + self.batch_size - 1)//self.batch_size}")
            batch_results = process_batch(batch)
            all_results.extend(batch_results)

            logger.info(f"Processed batch ({len(all_results)}/{len(urls)})")

        return all_results

    def save_results_to_csv(self, results: List[Dict], filename: str = 'indexation_results.csv'):
        """Save results to CSV file."""
        df = pd.DataFrame(results)
        df.to_csv(filename, index=False)
        logger.info(f"Results saved to {filename}")

def main():
    """Main function for standalone usage."""
    checker = GISChecker()
    results = checker.check_urls_from_file('urlinput.txt')
    checker.save_results_to_csv(results)
    logger.info("All URLs processed. Results saved to indexation_results.csv")

# Django usage example:
# from gis.gis_folder.gis_optimized import GISChecker
#
# def check_url_indexation(request):
#     checker = GISChecker()
#     url = request.GET.get('url')
#     result = checker.check_single_url(url)
#     return JsonResponse(result)
#
# def check_multiple_urls(request):
#     checker = GISChecker()
#     urls = request.POST.getlist('urls')
#     results = checker.check_urls_batch(urls)
#     return JsonResponse({'results': results})

if __name__ == "__main__":
    main()
