# GIS Indexation Checker - Selenium Version

This is a rewritten version of the GIS indexation checker that uses Selenium WebDriver instead of hrequests. It's designed to be more reliable and Django-compatible.

## Features

- **Selenium WebDriver**: Uses Chrome WebDriver with automatic driver management
- **Proxy Support**: Configured to use the provided Evomi proxy
- **CAPTCHA Detection**: Automatically detects CAPTCHAs and retries
- **Bot Detection Handling**: Returns "BotDetection" status when CAPTCHAs can't be overcome
- **Django Compatible**: Designed for easy integration with Django applications
- **Reliable Results**: Returns "Yes" for indexed, "No" for not indexed, "Error" for failures, "BotDetection" for CAPTCHA issues

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements_selenium.txt
```

2. The script will automatically download and manage ChromeDriver using webdriver-manager.

## Usage

### Standalone Usage

```python
from gis_optimized import GISChecker

# Create checker instance
checker = GISChecker()

# Check a single URL
result = checker.check_single_url("example.com")
print(result)  # {"url": "example.com", "indexation_status": "Yes"}

# Check multiple URLs
urls = ["example1.com", "example2.com", "example3.com"]
results = checker.check_urls_batch(urls)
print(results)

# Check URLs from file (original functionality)
results = checker.check_urls_from_file("urlinput.txt")
checker.save_results_to_csv(results, "output.csv")
```

### Django Integration

See `django_integration_example.py` for complete Django integration examples.

#### Quick Django Example:

```python
# views.py
from django.http import JsonResponse
from .gis_optimized import GISChecker

def check_url_view(request):
    url = request.GET.get('url')
    checker = GISChecker()
    result = checker.check_single_url(url)
    return JsonResponse(result)
```

#### URL Configuration:

```python
# urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('gis/check/', views.check_url_view, name='gis_check'),
]
```

#### API Usage:

```bash
# Check single URL
GET /gis/check/?url=example.com

# Response:
{"url": "example.com", "indexation_status": "Yes"}
```

### Celery Integration (Async Processing)

```python
from celery import shared_task
from .gis_optimized import GISChecker

@shared_task
def check_url_async(url):
    checker = GISChecker()
    return checker.check_single_url(url)
```

## Configuration

### Proxy Settings

The script is configured to use the Evomi proxy:
- Host: `core-residential.evomi.com`
- Port: `1000`
- Username: `it41`
- Password: `Zglx26lEvuyNSlO0Xrvh`

### Customization

You can customize the behavior by modifying the constants in the script:

```python
MAX_WORKERS = 3  # Number of concurrent workers
BATCH_SIZE = 5   # URLs per batch
RETRY_ATTEMPTS = 3  # Retry attempts for failed requests
DELAY_MIN = 2    # Minimum delay between requests
DELAY_MAX = 5    # Maximum delay between requests
TIMEOUT = 10     # Selenium timeout in seconds
```

## Response Format

The checker returns a dictionary with the following structure:

```python
{
    "url": "example.com",
    "indexation_status": "Yes|No|Error|BotDetection"
}
```

### Status Meanings:

- **"Yes"**: URL is indexed in Google
- **"No"**: URL is not indexed in Google
- **"Error"**: Technical error occurred during checking
- **"BotDetection"**: CAPTCHA detected and couldn't be overcome

## Testing

Run the test script to verify functionality:

```bash
python test_selenium_gis.py
```

## Reliability Features

1. **CAPTCHA Detection**: Automatically detects various CAPTCHA types
2. **Retry Logic**: Retries failed requests up to 3 times
3. **Cookie Handling**: Automatically accepts Google's cookie consent
4. **Multiple Result Selectors**: Uses multiple CSS selectors to find search results
5. **Proper Driver Cleanup**: Ensures WebDriver instances are properly closed
6. **Error Handling**: Comprehensive error handling and logging

## Performance Considerations

- **Reduced Concurrency**: Uses fewer workers (3) compared to the original (5) due to Selenium's resource requirements
- **Smaller Batches**: Processes 5 URLs per batch instead of 10
- **Longer Delays**: Uses longer delays (2-5 seconds) for better reliability
- **Resource Management**: Properly manages WebDriver instances to prevent memory leaks

## Troubleshooting

### Common Issues:

1. **ChromeDriver Issues**: The script automatically downloads ChromeDriver, but ensure you have Chrome installed
2. **Proxy Connection**: Verify the proxy credentials and connection
3. **Memory Usage**: Selenium uses more memory than hrequests; monitor system resources
4. **Rate Limiting**: If you encounter rate limiting, increase the delay values

### Logging:

The script includes comprehensive logging. Check the logs for detailed error information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Migration from hrequests

This version maintains the same API as the original hrequests version but with improved reliability:

- Same function signatures
- Same response format
- Added Django compatibility
- Better error handling
- CAPTCHA detection

## Dependencies

- `selenium>=4.15.0`: WebDriver automation
- `webdriver-manager>=4.0.0`: Automatic ChromeDriver management
- `pandas>=1.5.0`: Data processing (for CSV output)
