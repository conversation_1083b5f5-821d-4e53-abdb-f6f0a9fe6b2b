from django.shortcuts import render
from django.http import HttpResponse
from .models import SiteSource, Article, RSSArticle
from .autorevap_utils.api_parser import generate_csv_response, fig_to_html
from .autorevap_utils.analyze import analyze_authors, audit_articles, render_table_html
from .autorevap_utils.charts import (
    build_metrics_summary_df,
    plot_avg_links_heatmap, plot_avg_h2_heatmap, plot_avg_tema_links_heatmap,
    plot_stacked_theme_vs_article_links, plot_strong_tags_bar, plot_related_links_chart,
    plot_lazy_images_chart, plot_paragraph_count_chart, plot_paragraph_length_chart,
    plot_missing_elements_chart, plot_author_activity_over_time,
    plot_author_hourly_distribution, plot_author_weekday_distribution, plot_total_images_chart
)


def dashboard_view(request):
    sources_qs = SiteSource.objects.filter(is_active=True).order_by("site_name")
    if not sources_qs.exists():
        return HttpResponse("No active sites found", status=404)

    selected_site = request.GET.get("site") or sources_qs.first().shortcode
    selected_source = sources_qs.filter(shortcode=selected_site).first()
    if not selected_source:
        return HttpResponse("Selected site not found", status=404)

    source_type = request.GET.get("source", "api")
    export_format = request.GET.get("export")
    export_type = request.GET.get("type")

    model = RSSArticle if source_type == "rss" else Article

    if source_type == "rss":
        articles_qs = RSSArticle.objects.filter(source=selected_source).values(
            "id", "url", "title", "category", "published_at", "body_compressed",
            "link_count", "h2_count", "paragraph_count", "avg_paragraph_length",
            "author__name"
        )
    else:
        articles_qs = Article.objects.filter(source=selected_source).values(
            "id", "url", "title", "section", "section_slug", "link", "published_at",
            "modified_at", "body_compressed", "created_by", "canonical", "seo_title",
            "seo_description", "site_code", "tags", "images", "link_count",
            "tema_link_count", "h2_count", "strong_tag_count", "related_links_count",
            "lazy_img_count", "total_img_count", "paragraph_count", "avg_paragraph_length",
            "author__name"
        )


    articles = list(articles_qs)
    if not articles:
        return render(request, "fsn/fsn.html", {
            "websites": {s.site_name: s.shortcode for s in sources_qs},
            "selected_site": selected_site,
            "selected_name": selected_source.site_name,
            "selected_source": source_type,
            "no_data": True,
        })

    # Build metrics from DB fields
    metrics = [{
        "author": a.get("author__name") or "Unknown",
        "tema_links": a.get("tema_link_count", 0),
        "non_tema_links": (a.get("link_count", 0) - a.get("tema_link_count", 0)),
        "h2": a.get("h2_count", 0),
        "bold": a.get("strong_tag_count", 0),
        "related_links": a.get("related_links_count", 0),
        "lazy_imgs": a.get("lazy_img_count", 0),
        "total_imgs": a.get("total_img_count", 0),
        "paragraphs": a.get("paragraph_count", 0),
        "Avg Paragraph Length": a.get("avg_paragraph_length", 0),
        "words": int((a.get("avg_paragraph_length", 0)) * a.get("paragraph_count", 0)),
        "missing": {
            "bold": a.get("strong_tag_count", 0) == 0,
            "h2": a.get("h2_count", 0) == 0,
            "related": a.get("related_links_count", 0) == 0,
            "lazy_img": a.get("lazy_img_count", 0) == 0,
            "tema_link": a.get("tema_link_count", 0) == 0,
            "non_tema_link": (a.get("link_count", 0) - a.get("tema_link_count", 0)) == 0,
        }
    } for a in articles]



    summary_df = build_metrics_summary_df(metrics)

    # Build metadata only for hourly and weekday charts
    metadata = {
        a["id"]: {
            "url": a["url"],
            "title": a["title"],
            "published_at": a["published_at"],
            "author": [{"name": a["author__name"] or "Unknown"}]
        }
        for a in articles
    }

    # Tables and summary charts
    author_df, total_fig, avg_fig = analyze_authors(metadata, metrics)
    metrics_dict = {a["id"]: m for a, m in zip(articles, metrics)}
    article_df, audit_fig = audit_articles(metadata, metrics_dict)

    if export_format == "csv" and export_type in ["author", "article"]:
        df = author_df if export_type == "author" else article_df
        return generate_csv_response(df, filename=f"{export_type}_table.csv")

    summary_charts = [
        ("link_heatmap", plot_avg_links_heatmap),
        ("h2_heatmap", plot_avg_h2_heatmap),
        ("tema_heatmap", plot_avg_tema_links_heatmap),
        ("stacked_links_chart", plot_stacked_theme_vs_article_links),
        ("strong_tags_chart", plot_strong_tags_bar),
        ("related_links_chart", plot_related_links_chart),
        ("lazy_images_chart", plot_lazy_images_chart),
        ("paragraph_count_chart", plot_paragraph_count_chart),
        ("paragraph_length_chart", plot_paragraph_length_chart),
        ("total_images_chart", plot_total_images_chart),
    ]

    raw_charts = [
        ("missing_elements_chart", lambda: plot_missing_elements_chart(metrics)),
        ("author_activity_chart", lambda: plot_author_activity_over_time(metrics_dict)),
        ("hourly_distribution_chart", lambda: plot_author_hourly_distribution(metadata)),
        ("weekday_distribution_chart", lambda: plot_author_weekday_distribution(metadata)),
    ]

    context = {
        "websites": {s.site_name: s.shortcode for s in sources_qs},
        "selected_site": selected_site,
        "selected_name": selected_source.site_name,
        "selected_source": source_type,
        "author_table": render_table_html(author_df, table_id="authorTable"),
        "article_table": render_table_html(article_df, table_id="articleTable", drop_cols=["ID"]),
        "total_content_metrics_chart": fig_to_html(total_fig),
        "per_article_avg_metrics_chart": fig_to_html(avg_fig),
        "audit_chart": fig_to_html(audit_fig),
    }

    for key, func in summary_charts:
        context[key] = fig_to_html(func(summary_df))

    for key, func in raw_charts:
        context[key] = fig_to_html(func())

    return render(request, "fsn/fsn.html", context)
