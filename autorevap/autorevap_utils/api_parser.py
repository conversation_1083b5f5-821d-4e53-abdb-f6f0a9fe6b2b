import requests
import html
from django.http import HttpResponse
import plotly.io as pio
import zlib

# def fig_to_html(fig):
#     return pio.to_html(fig, full_html=False, include_plotlyjs=False)

def fig_to_html(fig):
    return pio.to_html(fig, full_html=True, include_plotlyjs="cdn")



def get_json_from_api(url):
    """
    Get JSON data from given API URL.

    Parameters
    ----------
    url : str
        URL of the API endpoint to query.

    Returns
    -------
    json_data : dict
        JSON data returned from the API.
    """
    response = requests.get(url)
    return response.json()

def get_items(json_data):
    """
    Extract a list of items from the given JSON data.

    Parameters
    ----------
    json_data : dict
        JSON data returned from the API.

    Returns
    -------
    items : list of dict
        List of items extracted from the JSON data.
    """
    return json_data["data"]["items"]

def parse_metadata(items):
    """
    Parse metadata from a list of items returned by the API.

    Parameters
    ----------
    items : list of dict
        List of items returned by the API.

    Returns
    -------
    metadata : dict of dict
        Dictionary of article metadata, where each key is the article ID and
        each value is another dictionary containing the metadata (title, URL, etc.).
    """
    metadata = {}
    access_keys = ["id", "url", "title", "section", "section_slug", "link", "published_at", "modified_at", "body", "created_by", "author", "canonical", "seo_title", "seo_description", "siteCode", "tags", "images",]

    for item in items:
        article_id = item["id"]
        metadata[article_id] = {}
        for key in access_keys:
            metadata[article_id][key] = item.get(key)

    return metadata

def decode_body(html_body):
    """
    Decode and unescape HTML body content.

    Supports raw strings or compressed bytes.
    """
    if isinstance(html_body, bytes):
        html_body = zlib.decompress(html_body).decode("utf-8")
    return html.unescape(html_body)
def extract_author_name(author_field):
    """
    Extract an author name from the given author field.

    The author field is usually a list of author dictionaries, but it can also
    be None or an empty list. If the author field is a list, return the name
    of the first author in the list. If the author field is not a list, or if
    the list is empty, return "Unknown".

    Parameters
    ----------
    author_field : list of dict
        List of author dictionaries, or None or an empty list.

    Returns
    -------
    author_name : str
        Author name, or "Unknown" if the author field is empty or None.
    """
    if isinstance(author_field, list) and author_field:
        return author_field[0].get("name", "Unknown")
    return "Unknown"


def generate_csv_response(df, filename="table.csv"):
    """
    Generate a CSV response from a pandas DataFrame and return it as a Django HttpResponse.

    This function creates a CSV file from the provided DataFrame and returns it as an
    attachment in an HttpResponse. The response is set with the appropriate content type
    for CSV files, and the filename for the attachment can be specified.

    Parameters
    ----------
    df : pandas.DataFrame
        The DataFrame to convert to a CSV file.
    filename : str, optional
        The filename to use for the CSV attachment (default is "table.csv").

    Returns
    -------
    HttpResponse
        A response containing the CSV as an attachment.
    """

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    df.to_csv(path_or_buf=response, index=False)
    return response