from bs4 import BeautifulSoup
from collections import defaultdict
from .api_parser import decode_body, extract_author_name
import pandas as pd

def build_metadata_and_metrics(articles, source_type="api"):
    metadata = {}
    metrics = {}

    for article in articles:
        article_id = article.get("external_id") if source_type == "api" else article.get("url")
        raw_body = article.get("body_compressed")
        decoded = decode_body(raw_body or "")
        soup = BeautifulSoup(decoded, "html.parser")

        author = article.get("author__name") or "Unknown"

        a_tags = soup.find_all("a", href=True)
        tema_links = [a for a in a_tags if "/tema/" in a["href"]]
        non_tema_links = [a for a in a_tags if "/tema/" not in a["href"]]

        metadata[article_id] = {
            "id": article_id,
            "url": article.get("url"),
            "title": article.get("title"),
            "section": article.get("section") or article.get("category") or "",
            "section_slug": article.get("section_slug", ""),
            "link": article.get("link", ""),
            "published_at": article.get("published_at"),
            "modified_at": article.get("modified_at"),
            "body": decoded,
            "created_by": article.get("created_by", ""),
            "canonical": article.get("canonical", ""),
            "seo_title": article.get("seo_title", ""),
            "seo_description": article.get("seo_description", ""),
            "siteCode": article.get("site_code", ""),
            "tags": article.get("tags", []),
            "images": article.get("images", []),
            "author": [{"name": author}],
        }

        metrics[article_id] = {
            "author": author,
            "paragraphs": len(soup.find_all("p")),
            "h2": len(soup.find_all("h2")),
            "bold": len(soup.find_all(['b', 'strong'])),
            "lazy_imgs": len(soup.find_all("img", loading="lazy")),
            "total_imgs": len(soup.find_all("img")),
            "tema_links": len(tema_links),
            "non_tema_links": len(non_tema_links),
            "related_links": len(soup.find_all("div", class_="wp_fsn_relatedlinks")),
            "words": len(soup.get_text().split()),
            "missing": {
                "bold": not soup.find(['b', 'strong']),
                "h2": not soup.find("h2"),
                "related": not soup.find("div", class_="wp_fsn_relatedlinks"),
                "lazy_img": not soup.find("img", loading="lazy"),
                "tema_link": not any("/tema/" in a["href"] for a in a_tags),
                "non_tema_link": not any("/tema/" not in a["href"] for a in a_tags),
            }
        }

    return metadata, metrics
