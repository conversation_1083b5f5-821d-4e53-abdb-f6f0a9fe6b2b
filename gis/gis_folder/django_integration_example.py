"""
Django integration example for GIS indexation checker.
This file shows how to integrate the Selenium-based GIS checker with Django views.
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import json
import logging
from .gis_optimized import GISChecker

logger = logging.getLogger(__name__)

class GISIndexationView(View):
    """Django class-based view for GIS indexation checking."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.checker = GISChecker()
    
    @method_decorator(csrf_exempt)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """Check a single URL via GET request."""
        url = request.GET.get('url')
        if not url:
            return JsonResponse({'error': 'URL parameter is required'}, status=400)
        
        try:
            result = self.checker.check_single_url(url)
            return JsonResponse(result)
        except Exception as e:
            logger.error(f"Error checking URL {url}: {str(e)}")
            return JsonResponse({'error': 'Internal server error'}, status=500)
    
    def post(self, request):
        """Check multiple URLs via POST request."""
        try:
            data = json.loads(request.body)
            urls = data.get('urls', [])
            
            if not urls:
                return JsonResponse({'error': 'URLs list is required'}, status=400)
            
            if len(urls) > 50:  # Limit batch size for safety
                return JsonResponse({'error': 'Maximum 50 URLs per request'}, status=400)
            
            results = self.checker.check_urls_batch(urls)
            return JsonResponse({'results': results})
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"Error checking URLs: {str(e)}")
            return JsonResponse({'error': 'Internal server error'}, status=500)

# Function-based views (alternative approach)
@csrf_exempt
@require_http_methods(["GET"])
def check_single_url(request):
    """Function-based view to check a single URL."""
    url = request.GET.get('url')
    if not url:
        return JsonResponse({'error': 'URL parameter is required'}, status=400)
    
    try:
        checker = GISChecker()
        result = checker.check_single_url(url)
        return JsonResponse(result)
    except Exception as e:
        logger.error(f"Error checking URL {url}: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def check_multiple_urls(request):
    """Function-based view to check multiple URLs."""
    try:
        data = json.loads(request.body)
        urls = data.get('urls', [])
        
        if not urls:
            return JsonResponse({'error': 'URLs list is required'}, status=400)
        
        if len(urls) > 50:  # Limit batch size for safety
            return JsonResponse({'error': 'Maximum 50 URLs per request'}, status=400)
        
        checker = GISChecker()
        results = checker.check_urls_batch(urls)
        return JsonResponse({'results': results})
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"Error checking URLs: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)

# Celery task example (if using Celery for async processing)
"""
from celery import shared_task

@shared_task
def check_url_async(url):
    '''Async task to check URL indexation.'''
    try:
        checker = GISChecker()
        result = checker.check_single_url(url)
        return result
    except Exception as e:
        logger.error(f"Error in async URL check {url}: {str(e)}")
        return {"url": url, "indexation_status": "Error", "error": str(e)}

@shared_task
def check_urls_batch_async(urls):
    '''Async task to check multiple URLs.'''
    try:
        checker = GISChecker()
        results = checker.check_urls_batch(urls)
        return results
    except Exception as e:
        logger.error(f"Error in async batch check: {str(e)}")
        return [{"url": url, "indexation_status": "Error", "error": str(e)} for url in urls]
"""

# URL patterns example for urls.py:
"""
from django.urls import path
from . import views

urlpatterns = [
    path('gis/check/', views.GISIndexationView.as_view(), name='gis_check'),
    path('gis/check-single/', views.check_single_url, name='gis_check_single'),
    path('gis/check-batch/', views.check_multiple_urls, name='gis_check_batch'),
]
"""

# Usage examples:
"""
# Single URL check (GET):
# GET /gis/check/?url=example.com

# Multiple URLs check (POST):
# POST /gis/check/
# Body: {"urls": ["example1.com", "example2.com", "example3.com"]}

# Response format:
# Single URL: {"url": "example.com", "indexation_status": "Yes|No|Error|BotDetection"}
# Multiple URLs: {"results": [{"url": "example1.com", "indexation_status": "Yes"}, ...]}
"""
