#!/usr/bin/env python3
"""
Test script for the Selenium-based GIS indexation checker.
"""

import sys
import os
import logging
from gis_optimized import <PERSON><PERSON><PERSON>he<PERSON>

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_url():
    """Test checking a single URL."""
    print("Testing single URL check...")
    checker = GISChecker()
    
    # Test with a well-known indexed site
    test_url = "google.com"
    result = checker.check_single_url(test_url)
    
    print(f"Result for {test_url}: {result}")
    assert result['url'] == test_url
    assert result['indexation_status'] in ['Yes', 'No', 'Error', 'BotDetection']
    print("✓ Single URL test passed")

def test_batch_urls():
    """Test checking multiple URLs."""
    print("\nTesting batch URL check...")
    checker = GISChecker()
    
    # Test with a mix of URLs
    test_urls = [
        "google.com",
        "github.com",
        "stackoverflow.com"
    ]
    
    results = checker.check_urls_batch(test_urls)
    
    print(f"Results for batch: {results}")
    assert len(results) == len(test_urls)
    
    for result in results:
        assert 'url' in result
        assert 'indexation_status' in result
        assert result['indexation_status'] in ['Yes', 'No', 'Error', 'BotDetection']
    
    print("✓ Batch URL test passed")

def test_with_file():
    """Test reading URLs from file."""
    print("\nTesting file-based URL check...")
    
    # Create a test file
    test_file = 'test_urls.txt'
    test_urls = ['google.com', 'github.com']
    
    with open(test_file, 'w') as f:
        for url in test_urls:
            f.write(f"{url}\n")
    
    try:
        checker = GISChecker()
        results = checker.check_urls_from_file(test_file)
        
        print(f"Results from file: {results}")
        assert len(results) == len(test_urls)
        
        for result in results:
            assert 'url' in result
            assert 'indexation_status' in result
            assert result['indexation_status'] in ['Yes', 'No', 'Error', 'BotDetection']
        
        print("✓ File-based test passed")
        
    finally:
        # Clean up test file
        if os.path.exists(test_file):
            os.remove(test_file)

def main():
    """Run all tests."""
    print("Starting GIS Selenium tests...")
    print("=" * 50)
    
    try:
        test_single_url()
        test_batch_urls()
        test_with_file()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        logger.error(f"Test error: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
