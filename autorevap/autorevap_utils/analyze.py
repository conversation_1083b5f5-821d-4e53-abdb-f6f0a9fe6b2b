import pandas as pd
import plotly.express as px
from collections import defaultdict
from .api_parser import extract_author_name

def analyze_authors(metadata, metrics):
    records = []
    for m in metrics:
        records.append({
            "Author": m["author"],
            "Paragraphs": m.get("paragraphs", 0),
            "Bold Tags": m.get("bold", 0),
            "Lazy Images": m.get("lazy_imgs", 0),
            "Tema Links": m.get("tema_links", 0)
        })

    df = pd.DataFrame(records)
    summary = df.groupby("Author").agg(
        Articles=("Paragraphs", "count"),
        paragraphs=("Paragraphs", "sum"),
        bold_tags=("Bold Tags", "sum"),
        lazy_images=("Lazy Images", "sum"),
        tema_links=("Tema Links", "sum")
    ).reset_index()

    summary["avg_paragraphs"] = (summary["paragraphs"] / summary["Articles"]).round(2)
    summary["avg_bold_tags"] = (summary["bold_tags"] / summary["Articles"]).round(2)
    summary["avg_lazy_images"] = (summary["lazy_images"] / summary["Articles"]).round(2)
    summary["avg_tema_links"] = (summary["tema_links"] / summary["Articles"]).round(2)

    total_melted = summary.melt(id_vars="Author", value_vars=["paragraphs", "bold_tags", "lazy_images", "tema_links"],
                                var_name="Metric", value_name="Count")
    avg_melted = summary.melt(id_vars="Author", value_vars=["avg_paragraphs", "avg_bold_tags", "avg_lazy_images", "avg_tema_links"],
                              var_name="Metric", value_name="Avg Count")

    fig1 = px.bar(total_melted, x="Count", y="Author", color="Metric", orientation="h",
                  title="Total Content Metrics by Author")
    fig2 = px.bar(avg_melted, x="Avg Count", y="Author", color="Metric", orientation="h",
                  title="Per-Article Averages by Author")

    return summary, fig1, fig2

def audit_articles(metadata, metrics):
    issues = []

    for article_id, article in metadata.items():
        m = metrics.get(article_id, {})
        author = extract_author_name(article.get("author"))
        title = article.get("title", "Untitled")
        url = article.get("url")
        published = article.get("published_at")

        missing = []
        if m.get("missing", {}).get("bold"): missing.append("No <strong>/<b>")
        if m.get("missing", {}).get("h2"): missing.append("No <h2>")
        if m.get("missing", {}).get("related"): missing.append("No related links div")
        if m.get("missing", {}).get("lazy_img"): missing.append("No lazy-loaded image")
        if m.get("missing", {}).get("tema_link"): missing.append("No /tema/ link")

        issues.append({
            "ID": article_id,
            "URL": url,
            "Title": title,
            "Author": author,
            "Published": published,
            "Missing Elements": ", ".join(missing),
            "Paragraphs": m.get("paragraphs", 0),
            "Words": m.get("words", 0),
        })

    df = pd.DataFrame(issues)
    df["Published"] = pd.to_datetime(df["Published"], errors='coerce')
    df["Date Published"] = df["Published"].dt.date.astype(str)
    df["Time Published"] = df["Published"].dt.time.astype(str)
    df.drop(columns=["Published"], inplace=True)
    df["Missing Count"] = df["Missing Elements"].apply(lambda x: len(x.split(", ")) if x else 0)

    fig = px.histogram(df, x="Missing Count", nbins=int(df["Missing Count"].max()) + 1,
                       title="Distribution of Missing Elements in Articles")
    return df, fig

def render_table_html(df, table_id="dataTable", drop_cols=None, classes="table table-striped text-center"):
    if drop_cols:
        df = df.drop(columns=drop_cols, errors="ignore")
    return df.to_html(classes=classes, index=False, table_id=table_id)
