import pandas as pd
from collections import defaultdict
from .plotly_utils import plot_heatmap, plot_barh, plot_stacked_barh
import plotly.graph_objects as go


def build_metrics_summary_df(metrics: list) -> pd.DataFrame:
    df = pd.DataFrame.from_records(metrics)
    if df.empty:
        return pd.DataFrame()

    summary = df.groupby("author").agg({
        "tema_links": "mean",
        "non_tema_links": "mean",
        "h2": "mean",
        "bold": "mean",
        "related_links": "mean",
        "lazy_imgs": "mean",
        "total_imgs": "mean",
        "paragraphs": "mean",
        "Avg Paragraph Length": "mean"
    }).reset_index()

    summary = summary.rename(columns={"author": "Author"})
    return summary



def plot_avg_links_heatmap(summary_df):
    df = summary_df.copy()
    df["Avg Links"] = df["tema_links"] + df["non_tema_links"]
    df = df[["Author", "Avg Links"]].set_index("Author")
    return plot_heatmap(df, title="Heatmap of Average Links per Author")


def plot_avg_h2_heatmap(summary_df):
    df = summary_df[["Author", "h2"]].rename(columns={"h2": "Avg H2"}).set_index("Author")
    return plot_heatmap(df, title="Heatmap of Average <h2> Tags per Author")


def plot_avg_tema_links_heatmap(summary_df):
    df = summary_df[["Author", "tema_links"]].rename(columns={"tema_links": "Avg Tema Links"}).set_index("Author")
    return plot_heatmap(df, title="Heatmap of Average '/tema/' Links per Author")


def plot_stacked_theme_vs_article_links(summary_df):
    df = summary_df[["Author", "tema_links", "non_tema_links"]].copy()
    df = df.rename(columns={"tema_links": "tema", "non_tema_links": "notema"})
    return plot_stacked_barh(df, y_col="Author", x1_col="tema", x2_col="notema",
                             label1="Enlaces a tags", label2="Enlaces a artículos",
                             title="Links with and without '/tema/' per Author")


def plot_strong_tags_bar(summary_df):
    df = summary_df[["Author", "bold"]].rename(columns={"bold": "Avg <strong>"})
    return plot_barh(df, x_col="Avg <strong>", y_col="Author",
                     title="Average <strong> Tags per Author", color="lightseagreen")


def plot_related_links_chart(summary_df):
    df = summary_df[["Author", "related_links"]].rename(columns={"related_links": "Avg Related Links"})
    return plot_barh(df, x_col="Avg Related Links", y_col="Author",
                     title="Average Related Links per Author", color="royalblue")


def plot_lazy_images_chart(summary_df):
    df = summary_df[["Author", "lazy_imgs"]].rename(columns={"lazy_imgs": "Avg Lazy Images"})
    return plot_barh(df, x_col="Avg Lazy Images", y_col="Author",
                     title="Average <img loading='lazy'> per Author", color="tomato")


def plot_total_images_chart(summary_df):
    df = summary_df[["Author", "total_imgs"]].rename(columns={"total_imgs": "Avg Total Images"})
    return plot_barh(df, x_col="Avg Total Images", y_col="Author",
                     title="Average <img> Tags per Author", color="darkcyan")


def plot_paragraph_length_chart(summary_df):
    df = summary_df[["Author", "Avg Paragraph Length"]]
    return plot_barh(df, x_col="Avg Paragraph Length", y_col="Author",
                     title="Average Characters per Paragraph per Author", color="lightcoral")


def plot_paragraph_count_chart(summary_df):
    df = summary_df[["Author", "paragraphs"]].rename(columns={"paragraphs": "Avg Paragraphs"})
    return plot_barh(df, x_col="Avg Paragraphs", y_col="Author",
                     title="Average Paragraph Count per Author", color="lightblue")


def plot_missing_elements_chart(metrics):
    missing_counts = defaultdict(int)

    for m in metrics:
        for key, missing in m.get("missing", {}).items():
            if missing:
                label = {
                    "bold": "Bold tags (<b>/<strong>)",
                    "h2": "Header <h2>",
                    "related": "Div 'wp_fsn_relatedlinks'",
                    "lazy_img": "Lazy-loaded Image",
                    "tema_link": "Link with '/tema/'",
                    "non_tema_link": "Link without '/tema/'"
                }.get(key, key)
                missing_counts[label] += 1

    df = pd.DataFrame({
        "Missing Element": list(missing_counts.keys()),
        "Count": list(missing_counts.values())
    })

    return plot_barh(df, x_col="Count", y_col="Missing Element",
                     title="Number of Missing Elements in Articles", color="skyblue")


def plot_author_activity_over_time(metrics):
    author_counts = defaultdict(int)
    for m in metrics.values():
        author_counts[m["author"]] += 1
    df = pd.DataFrame({"Author": list(author_counts.keys()),
                       "Articles": list(author_counts.values())})
    df = df.sort_values("Articles")
    return plot_barh(df, x_col="Articles", y_col="Author",
                     title="Total Number of Articles Published per Author", color="mediumseagreen")


def plot_author_hourly_distribution(metadata):
    hourly_counts = defaultdict(lambda: defaultdict(int))
    for article in metadata.values():
        author = article.get("author", [{}])[0].get("name", "Unknown")
        published_at = article.get("published_at")
        if published_at:
            try:
                hour = pd.to_datetime(published_at).hour
                hourly_counts[author][hour] += 1
            except Exception:
                continue
    df = pd.DataFrame(hourly_counts).T.fillna(0).astype(int)
    df = df[[h for h in range(24) if h in df.columns]]
    df.columns = df.columns.astype(str)
    df.index = df.index.astype(str)
    fig = go.Figure(data=go.Heatmap(
        z=df.values,
        x=df.columns.tolist(),
        y=df.index.tolist(),
        text=df.values,
        texttemplate="%{text}",
        colorscale="YlGnBu",
        hoverongaps=False
    ))
    fig.update_layout(
        title="Articles by Hour per Author",
        xaxis_title="Hour",
        yaxis_title="Author",
        height=600 + len(df) * 20,
        width=1200,
        margin=dict(t=60, b=40)
    )
    return fig


def plot_author_weekday_distribution(metadata):
    weekday_counts = defaultdict(lambda: defaultdict(int))
    weekday_labels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    for article in metadata.values():
        author = article.get("author", [{}])[0].get("name", "Unknown")
        published_at = article.get("published_at")
        if published_at:
            try:
                weekday = pd.to_datetime(published_at).weekday()
                weekday_counts[author][weekday] += 1
            except Exception:
                continue
    df = pd.DataFrame(weekday_counts).T.fillna(0).astype(int)
    df.columns = [weekday_labels[i] for i in df.columns]
    df = df[[day for day in weekday_labels if day in df.columns]]
    df.index = df.index.astype(str)
    fig = go.Figure(data=go.Heatmap(
        z=df.values,
        x=df.columns.tolist(),
        y=df.index.tolist(),
        text=df.values,
        texttemplate="%{text}",
        colorscale="YlGnBu",
        hoverongaps=False
    ))
    fig.update_layout(
        title="Articles by Weekday per Author",
        xaxis_title="Day",
        yaxis_title="Author",
        height=600 + len(df) * 20,
        width=1200,
        margin=dict(t=60, b=40)
    )
    return fig
